import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useEffect, useState } from "react";
import {
  FiLayers,
  FiDollarSign,
  FiMapPin,
  FiCheckCircle,
  FiDownload,
  FiHome,
  FiCalendar,
  FiStar,
  FiInfo,
} from "react-icons/fi";
import axios from "axios";
import Email from "../../../../layout/sections/Email";
import FooterSection from "../../../../layout/sections/Footer";
import FooterWithEmail from "../../../../layout/FooterWithEmail";

function PropertyDetail() {
  const { id } = useParams();
  const [property, setProperty] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProperty = async () => {
      setLoading(true);
      try {
        const response = await axios.get(
          `http://localhost:3001/api/projects/${id}`
        );
        setProperty(response.data);
      } catch (error) {
        console.error("Failed to fetch property:", error);
        setProperty(null);
      } finally {
        setLoading(false);
      }
    };
    fetchProperty();
  }, [id]);

  if (loading)
    return (
      <div className="min-h-screen bg-[#EBE6E2] flex items-center justify-center pt-55">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-8 border-primary border-t-transparent rounded-full animate-spin mb-4" />
          <div className="text-2xl font-semibold text-primary animate-pulse">
            Loading property details...
          </div>
        </div>
      </div>
    );

  if (!property)
    return (
      <div className="min-h-screen bg-[#EBE6E2] p-10 pt-55">
        <div className="max-w-5xl mx-auto">
          <Link
            to="/destinations"
            className="text-primary hover:underline mb-8 inline-block"
          >
            ← Back to properties
          </Link>
          <div className="text-3xl">Property not found</div>
        </div>
      </div>
    );

  const mainUnit = property.unit_types?.[0];
  const startingPrice = mainUnit
    ? `AED ${mainUnit.starting_price.toLocaleString()}`
    : "N/A";

  return (
    <div className="min-h-screen bg-[#EBE6E2] pt-55">
      {/* Hero Section */}
      <div className="w-full h-[65vh] relative flex items-end mb-10">
        <img
          src={property.projectHero || property.mainImage}
          alt={property.title}
          className="w-full h-full object-cover absolute inset-0 z-0"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent z-10" />
        <div className="relative z-20 px-8 pb-10 max-w-4xl mx-auto w-full">
          <h1 className="text-5xl font-extrabold text-white mb-2 drop-shadow-lg">
            {property.title}
          </h1>
          {property.subtitle && (
            <h2 className="text-2xl md:text-3xl text-primary font-semibold italic mb-2 drop-shadow-lg">
              {property.subtitle}
            </h2>
          )}
          {property.summary && (
            <p className="text-xl md:text-2xl text-white font-medium drop-shadow mb-2 max-w-2xl">
              {property.summary}
            </p>
          )}
        </div>
      </div>

      <div className="mx-auto px-4 md:px-8 py-10 max-w-7xl">
        <Link
          to="/destinations"
          className="text-primary hover:underline mb-8 inline-block text-lg font-medium"
        >
          ← Back to properties
        </Link>

        {/* 1. First Row: Gallery (Left) + Main Info (Right) */}
        {property.gallery && property.gallery.length > 0 && (
          <section className="mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Gallery Section - Left */}
              <div className="lg:order-1">
                <img
                  src={property.gallery[0]}
                  alt="Gallery 1"
                  className="w-full h-[500px] object-cover rounded-2xl shadow-lg border-2 border-gray-200 transition-all duration-500 hover:object-scale-110"
                />
              </div>

              {/* Main Info & Description Section - Right */}
              <div className="lg:order-2">
                <section className="bg-white rounded-2xl shadow-xl p-10 h-full flex flex-col justify-between">
                  <div>
                    <div className="flex items-center gap-3 mb-4">
                      <FiHome className="text-3xl text-primary" />
                      <h1 className="text-4xl font-extrabold text-gray-900">
                        {property.title}
                      </h1>
                    </div>

                    {property.subtitle && (
                      <div className="flex items-center gap-3 mb-6">
                        <FiStar className="text-2xl text-primary" />
                        <div className="text-2xl text-primary font-semibold italic">
                          {property.subtitle}
                        </div>
                      </div>
                    )}

                    {property.summary && (
                      <div className="flex items-start gap-3 mb-6">
                        <FiInfo className="text-xl text-primary mt-1" />
                        <div className="text-lg text-gray-700 leading-relaxed">
                          {property.summary}
                        </div>
                      </div>
                    )}

                    {property.desc && (
                      <div className="flex items-start gap-3 mb-8">
                        <FiInfo className="text-xl text-primary mt-1" />
                        <p className="text-lg text-gray-700 leading-relaxed">
                          {property.desc}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-6">
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-full text-lg font-semibold">
                        <FiHome className="text-xl" />
                        {property.type}
                      </div>
                      <div className="flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-full text-lg font-semibold">
                        <FiCalendar className="text-xl" />
                        Handover: {property.handover}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-3xl font-bold text-primary bg-primary/10 px-8 py-4 rounded-full shadow">
                        {startingPrice}
                      </div>
                      {property.brochure && (
                        <a
                          href={property.brochure}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-3 text-primary hover:underline text-lg font-semibold bg-primary/10 px-6 py-3 rounded-full transition-colors hover:bg-primary/20"
                        >
                          <FiDownload className="text-2xl" />
                          Download Brochure
                        </a>
                      )}
                    </div>
                  </div>
                </section>
              </div>
            </div>
          </section>
        )}

        {/* 2. Second Row: Unit Types (Left) + Gallery (Right) */}
        {property.unit_types && property.unit_types.length > 0 && (
          <section className="mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Unit Types & Prices Section - Left */}
              <div className="lg:order-1">
                <section className="bg-white p-10 rounded-2xl shadow-md h-full flex flex-col">
                  <div className="flex items-center gap-3 mb-6">
                    <FiLayers className="text-3xl text-primary" />
                    <h3 className="text-3xl font-bold text-primary border-b-4 border-primary pb-2">
                      Unit Types & Prices
                    </h3>
                  </div>

                  <div className="flex-1 overflow-x-auto">
                    <table className="min-w-full text-lg">
                      <thead>
                        <tr className="text-left border-b-2 border-primary">
                          <th className="py-4 pr-8 text-xl font-bold text-primary">
                            Type
                          </th>
                          <th className="py-4 pr-8 text-xl font-bold text-primary">
                            Size (sqft)
                          </th>
                          <th className="py-4 pr-8 text-xl font-bold text-primary">
                            Starting Price
                          </th>
                        </tr>
                      </thead>
                      <tbody className="space-y-2">
                        {property.unit_types.map((unit, index) => (
                          <tr
                            key={unit._id}
                            className={`border-b last:border-b-0 hover:bg-gray-50 transition-colors ${
                              index % 2 === 0 ? "bg-gray-50/50" : ""
                            }`}
                          >
                            <td className="py-4 pr-8 font-semibold text-lg">
                              {unit.type}
                            </td>
                            <td className="py-4 pr-8 text-lg">
                              {unit.size_range_sqft}
                            </td>
                            <td className="py-4 pr-8 text-primary font-bold text-xl">
                              AED {unit.starting_price.toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  <div className="mt-6 pt-6 border-t-2 border-primary/20">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <FiDollarSign className="text-2xl text-primary" />
                        <span className="text-lg font-semibold text-gray-700">
                          Total Unit Types: {property.unit_types.length}
                        </span>
                      </div>
                      <div className="text-2xl font-bold text-primary">
                        Starting from {startingPrice}
                      </div>
                    </div>
                  </div>
                </section>
              </div>

              {/* Gallery Section - Right */}
              {property.gallery && property.gallery.length > 1 && (
                <div className="lg:order-2">
                  <img
                    src={property.gallery[1]}
                    alt="Gallery 2"
                    className="w-full h-[500px] object-cover rounded-2xl shadow-lg border-2 border-gray-200 transition-all duration-500 hover:object-scale-110"
                  />
                </div>
              )}
            </div>
          </section>
        )}

        {/* 3. Full Width: Nearby & Location Section */}
        <section className="bg-[#F7F6F3] p-8 rounded-2xl shadow-md mb-12">
          <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-primary border-b-2 border-primary pb-1">
            <FiMapPin className="text-primary" /> Location & Nearby
          </h3>
          <div className="mb-2 text-lg">
            <span className="font-semibold">Area:</span>{" "}
            {property.location?.area}
          </div>
          <div className="mb-2 text-lg">
            <span className="font-semibold">City:</span>{" "}
            {property.location?.city}
          </div>
          {property.location?.nearby && property.location.nearby.length > 0 && (
            <div className="mb-2">
              <div className="font-semibold mb-1">Nearby:</div>
              <div className="flex flex-wrap gap-2">
                {property.location.nearby.map((n, i) => (
                  <span
                    key={i}
                    className="bg-primary/10 text-primary px-3 py-1 rounded-full text-base font-medium"
                  >
                    {n}
                  </span>
                ))}
              </div>
            </div>
          )}
        </section>

        {/* 5. Amenities Section */}
        <section className="bg-[#F7F6F3] p-8 rounded-2xl shadow-md mb-12">
          <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-primary border-b-2 border-primary pb-1">
            <FiCheckCircle className="text-primary" /> Amenities
          </h3>
          <ul className="list-disc pl-6 space-y-2 text-lg">
            {property.amenities?.map((a, i) => (
              <li key={i}>{a}</li>
            ))}
          </ul>
        </section>

        {/* 6. Payment Plan Section */}
        {property.payment_plan && (
          <section className="bg-white p-8 rounded-2xl shadow-md mb-12">
            <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-primary border-b-2 border-primary pb-1">
              <FiDollarSign className="text-primary" /> Payment Plan
            </h3>
            <div className="mb-2 text-lg font-semibold">
              {property.payment_plan.summary}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-4">
              <div className="bg-primary/10 rounded-lg p-6 flex flex-col items-center">
                <span className="text-primary font-bold text-lg mb-1">
                  Down Payment
                </span>
                <span className="text-2xl font-extrabold">
                  {property.payment_plan.details.down_payment_percent}%
                </span>
              </div>
              <div className="bg-primary/10 rounded-lg p-6 flex flex-col items-center">
                <span className="text-primary font-bold text-lg mb-1">
                  During Construction
                </span>
                <span className="text-2xl font-extrabold">
                  {property.payment_plan.details.during_construction_percent}%
                </span>
              </div>
              <div className="bg-primary/10 rounded-lg p-6 flex flex-col items-center">
                <span className="text-primary font-bold text-lg mb-1">
                  Post-Handover
                </span>
                <span className="text-2xl font-extrabold">
                  {property.payment_plan.details.post_handover_percent}%
                </span>
              </div>
            </div>
            {property.payment_plan.details.monthly_installments && (
              <div className="mt-4 text-base text-gray-700">
                Monthly Installments for{" "}
                <span className="font-semibold">
                  {property.payment_plan.details.total_months}
                </span>{" "}
                months
              </div>
            )}
          </section>
        )}

        {/* 7. CTA Section */}
        {/* {property.call_to_action && (
          <section className="bg-primary rounded-2xl p-10 flex flex-col items-center gap-4 mt-4 shadow-xl">
            <div className="text-2xl font-bold text-white mb-2">
              Consultant: {property.call_to_action.consultant}
            </div>
            <div className="flex flex-wrap gap-6 justify-center">
              {property.call_to_action.actions.map((action) => (
                <a
                  key={action._id}
                  href={action.url}
                  target="_blank"
                  rel="noreferrer"
                  className="bg-white text-primary py-4 px-10 rounded-xl text-xl hover:bg-opacity-90 transition-all shadow-lg font-bold border-2 border-primary"
                >
                  {action.label}
                </a>
              ))}
            </div>
          </section>
        )} */}
      </div>
      {/* <Email/> */}
      <FooterWithEmail/>
    </div>
  );
}

export default PropertyDetail;
